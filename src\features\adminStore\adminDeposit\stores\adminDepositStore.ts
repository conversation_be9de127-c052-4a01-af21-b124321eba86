import { create } from 'zustand';
import { SelectorData } from '../types';

interface AdminDepositStore {
  // Selected filter names for display
  selectedAreaName: string | null;
  selectedSubAreaName: string | null;
  selectedMerchantName: string | null;
  
  // Actions
  setSelectedNames: (areaId?: string, subAreaId?: string, merchantId?: string) => void;
  getAreaName: (areaId: string) => string | null;
  getSubAreaName: (subAreaId: string) => string | null;
  getMerchantName: (merchantId: string) => string | null;
  clearSelectedNames: () => void;
}

export const useAdminDepositStore = create<AdminDepositStore>((set, get) => ({
  selectedAreaName: null,
  selectedSubAreaName: null,
  selectedMerchantName: null,

  setSelectedNames: (areaId?: string, subAreaId?: string, merchantId?: string) => {
    const { selectorData } = get();
    if (!selectorData) return;

    let areaName: string | null = null;
    let subAreaName: string | null = null;
    let merchantName: string | null = null;

    // Get area name
    if (areaId && areaId !== 'all') {
      const area = selectorData.agxAreaFormUsers.find(a => a.agx_areaid === areaId);
      areaName = area?.agxAreaName || null;
    }

    // Get sub area name
    if (subAreaId && subAreaId !== 'all') {
      const subArea = selectorData.agxSubAreas.find(sa => sa.agxSubAreaid === subAreaId);
      subAreaName = subArea?.agxSubAreaName || null;
    }

    // Get merchant name
    if (merchantId && merchantId !== 'all') {
      const merchant = selectorData.merchants.find(m => m.agxMerchantNo === merchantId);
      merchantName = merchant?.agxStoreName || null;
    }

    set({
      selectedAreaName: areaName,
      selectedSubAreaName: subAreaName,
      selectedMerchantName: merchantName,
    });
  },

  getAreaName: (areaId: string) => {
    const { selectorData } = get();
    if (!selectorData || !areaId || areaId === 'all') return null;
    
    const area = selectorData.agxAreaFormUsers.find(a => a.agx_areaid === areaId);
    return area?.agxAreaName || null;
  },

  getSubAreaName: (subAreaId: string) => {
    const { selectorData } = get();
    if (!selectorData || !subAreaId || subAreaId === 'all') return null;
    
    const subArea = selectorData.agxSubAreas.find(sa => sa.agxSubAreaid === subAreaId);
    return subArea?.agxSubAreaName || null;
  },

  getMerchantName: (merchantId: string) => {
    const { selectorData } = get();
    if (!selectorData || !merchantId || merchantId === 'all') return null;
    
    const merchant = selectorData.merchants.find(m => m.agxMerchantNo === merchantId);
    return merchant?.agxStoreName || null;
  },

  clearSelectedNames: () => {
    set({
      selectedAreaName: null,
      selectedSubAreaName: null,
      selectedMerchantName: null,
    });
  },
}));
