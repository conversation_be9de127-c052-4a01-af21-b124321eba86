import { create } from 'zustand';
import { SelectorData } from '../types';

interface AdminDepositStore {
  // Selected filter names for display
  selectedAreaName: string | null;
  selectedSubAreaName: string | null;
  selectedMerchantName: string | null;

  // Actions
  setSelectedNames: (areaName?: string, subAreaName?: string, merchantName?: string) => void;
  clearSelectedNames: () => void;
}

export const useAdminDepositStore = create<AdminDepositStore>((set) => ({
  selectedAreaName: null,
  selectedSubAreaName: null,
  selectedMerchantName: null,

  setSelectedNames: (areaName?: string, subAreaName?: string, merchantName?: string) => {
    set({
      selectedAreaName: areaName || null,
      selectedSubAreaName: subAreaName || null,
      selectedMerchantName: merchantName || null,
    });
  },

  clearSelectedNames: () => {
    set({
      selectedAreaName: null,
      selectedSubAreaName: null,
      selectedMerchantName: null,
    });
  },
}));
